"use client";
import ConversationHistory from "@/components/rabbai/ConversationHistory";
import { studyGuideConversations } from "@/data/rabbai";

const StudyGuideLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div>
      <ConversationHistory conversations={studyGuideConversations} />
      <div className="flex-1 px-4 lg:ml-[25rem] lg:px-0">{children}</div>
    </div>
  );
};

export default StudyGuideLayout;
