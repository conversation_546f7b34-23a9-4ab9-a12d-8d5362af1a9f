@import "tailwindcss";

@theme {
  --color-primary: #fbbc04;
  --color-secondary: #020213;
  --color-white: #fff;
  --color-dark: #1e1c2b;
  --color-off-white: #d9d9d9;
  --color-lilac: #e9d7fe;
  --color-emphasis: #fbbc04;
  --text-xxs: 10px;
  --breakpoint-xs: 500px;
  --breakpoint-xxs: 350px;
  --nav-height: 64px;
}

body {
  background-color: var(--color-secondary);
  color: var(--color-white);
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #fbbc04 transparent;
  overflow-y: auto !important;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
  margin: 4px 0;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #fbbc04 0%, #d4a004 100%);
  border-radius: 4px;
  border: 1px solid rgba(251, 188, 4, 0.3);
  transition: all 0.2s ease;
  min-height: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #ffc933 0%, #e6b005 100%);
  border-color: rgba(251, 188, 4, 0.5);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Hide scrollbar arrows/buttons */
.custom-scrollbar::-webkit-scrollbar-button {
  display: none;
}
