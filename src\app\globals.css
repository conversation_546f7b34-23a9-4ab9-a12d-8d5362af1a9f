@import "tailwindcss";

@theme {
  --color-primary: #fbbc04;
  --color-secondary: #020213;
  --color-white: #fff;
  --color-dark: #1e1c2b;
  --color-off-white: #d9d9d9;
  --color-lilac: #e9d7fe;
  --color-emphasis: #fbbc04;
  --text-xxs: 10px;
  --breakpoint-xs: 500px;
  --breakpoint-xxs: 350px;
  --nav-height: 64px;
}

body {
  background-color: var(--color-secondary);
  color: var(--color-white);
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.4) transparent;
  overflow-y: auto !important;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  min-height: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.6);
  border-color: rgba(255, 255, 255, 0.2);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Force scrollbar to always be visible for testing */
.custom-scrollbar::-webkit-scrollbar-thumb:window-inactive {
  background-color: rgba(255, 255, 255, 0.2);
}
